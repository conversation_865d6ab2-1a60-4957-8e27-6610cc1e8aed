<template>
    <div>
        <el-card shadow="hover">
            <el-form label-width="20px" :inline="true" size="mini">
                <el-form-item>
                    <el-select v-model="selectType">
                        <el-option
                            v-for="item in typeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="keyword"
                        placeholder="请输入关键词"
                        @keyup.enter.native="queryProduct"
                        clearable
                    ></el-input>
                </el-form-item>
                <!-- <el-form-item>
                    <el-select
                        v-model="issyncwms"
                        placeholder="是否同步萌牙"
                        clearable
                    >
                        <el-option label="已同步" :value="1"> </el-option>
                        <el-option label="同步失败" :value="0"> </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item>
                    <el-select
                        v-model="mid"
                        placeholder="请选择商家"
                        clearable
                        filterable
                        @change="queryProduct"
                    >
                        <el-option
                            v-for="item in merchantOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="warning" @click="queryProduct"
                        >查询</el-button
                    >
                    <el-button type="primary" @click="addProcution"
                        >添加产品库</el-button
                    >
                    <el-button
                        type="success"
                        @click="batch_upload_visible = true"
                        >批量上传</el-button
                    >
                    <el-button
                        type="primary"
                        @click="uploadCustomsVisible = true"
                        >批量上传关单</el-button
                    >
                    <!-- <el-button
                        type="success"
                        @click="pushTP"
                        :disabled="multilple.length == 0"
                        >同步萌牙</el-button
                    > -->
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover">
            <div class="table">
                <el-table
                    :data="tableData"
                    border
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                    size="mini"
                    @selection-change="handleSelectionChange"
                >
                    <!-- <el-table-column type="selection" width="55">
                    </el-table-column> -->
                    <el-table-column type="expand">
                        <template slot-scope="props">
                            <el-form
                                label-position="left"
                                :inline="true"
                                label-width="120px"
                                class="demo-table-expand"
                            >
                                <el-form-item label="商品类型">
                                    <span>{{
                                        props.row.product_category_name +
                                            "-" +
                                            props.row.product_type_name || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="采摘年份"
                                    v-if="props.row.product_category == 1"
                                >
                                    <span>{{
                                        props.row.grape_picking_years || "-"
                                    }}</span>
                                </el-form-item>

                                <el-form-item
                                    label="残糖"
                                    v-if="props.row.product_category == 1"
                                >
                                    <span>{{
                                        props.row.residual_sugar
                                            ? props.row.residual_sugar + "g/L"
                                            : "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item label="国家">
                                    <span>{{
                                        props.row.country_name || "-"
                                    }}</span>
                                </el-form-item>

                                <el-form-item label="产区">
                                    <span>{{
                                        props.row.producing_area_name || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="酒精度"
                                    v-if="props.row.product_category == 1"
                                >
                                    <span>{{
                                        props.row.alcohol != "" &&
                                        props.row.alcohol != null
                                            ? props.row.alcohol + "%vol"
                                            : "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="酒庄"
                                    v-if="props.row.product_category == 1"
                                >
                                    <span>{{
                                        props.row.chateau_name || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="规格"
                                    v-if="props.row.product_category != 5"
                                >
                                    <span>{{ props.row.capacity || "-" }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="关键词"
                                    v-if="props.row.product_category == 1"
                                >
                                    <span>{{
                                        props.row.product_keywords
                                    }}</span>
                                </el-form-item>
                                <el-form-item label="商品单位">
                                    <span>{{
                                        props.row.product_unit_name || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="保质期"
                                    v-if="
                                        props.row.product_category == 1 ||
                                        props.row.product_category == 2 ||
                                        props.row.product_category == 3
                                    "
                                >
                                    <span>{{
                                        props.row.shelf_life + "天" || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item label="酿造工艺">
                                    <span>{{ props.row.brewing || "-" }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="罐装日期"
                                    v-if="
                                        props.row.product_category == 1 ||
                                        props.row.product_category == 2
                                    "
                                >
                                    <span>{{
                                        props.row.canning_years || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="箱规"
                                    v-if="
                                        props.row.product_category == 1 ||
                                        props.row.product_category == 2
                                    "
                                >
                                    <span>{{
                                        props.row.carton_dimension || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item label="Tasting Notes">
                                    <span>{{
                                        props.row.tasting_notes || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="商品形态"
                                    v-if="props.row.product_category != 5"
                                >
                                    <!-- {{props.row.product_form}} -->
                                    <span>{{
                                        props.row.product_form !== ""
                                            ? props.row.product_form == 0
                                                ? "液体"
                                                : "固体"
                                            : "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="成本"
                                    v-if="props.row.product_category != 5"
                                >
                                    <span>{{
                                        props.row.costprice || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="整箱成本"
                                    v-if="props.row.product_category != 5"
                                >
                                    <span>{{
                                        props.row.cost_container || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="税率"
                                    v-if="props.row.product_category != 5"
                                >
                                    <span>{{ props.row.tax_rate || "-" }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="评分"
                                    v-if="props.row.product_category == 1"
                                >
                                    <span>{{ props.row.score || "-" }}</span>
                                </el-form-item>
                                <!-- <el-form-item label="是否赠品">
                                    <span>{{
                                        props.row.is_gift
                                            ? props.row.is_gift == 1
                                                ? "是"
                                                : "否"
                                            : "-"
                                    }}</span>
                                </el-form-item> -->
                                <el-form-item label="指令商品">
                                    <span>{{
                                        props.row.is_addition == 1
                                            ? "是"
                                            : "否" || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="获奖"
                                    v-if="props.row.product_category == 1"
                                >
                                    <span>{{ props.row.prize || "-" }}</span>
                                </el-form-item>
                                <el-form-item label="创建时间">
                                    <span>{{
                                        props.row.created_time || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item label="创建人">
                                    <span>{{ props.row.adname || "-" }}</span>
                                </el-form-item>
                                <el-form-item label="最近修改时间">
                                    <span>{{
                                        props.row.update_time || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item label="最近修改人">
                                    <span>{{
                                        props.row.reviser_name || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="重量"
                                    v-if="props.row.product_category != 5"
                                >
                                    <span>{{
                                        props.row.carton_dimension || "-"
                                    }}</span>
                                </el-form-item>
                                <el-form-item
                                    label="饮用建议"
                                    v-if="props.row.product_category == 1"
                                >
                                    <span>{{
                                        props.row.drinking_suggestion || "-"
                                    }}</span>
                                </el-form-item>
                            </el-form>
                        </template>
                    </el-table-column>

                    <el-table-column prop="bar_code" label="条码" width="200">
                    </el-table-column>
                    <el-table-column prop="short_code" label="简码" width="200">
                    </el-table-column>
                    <el-table-column prop="cn_product_name" label="中文品名">
                    </el-table-column>
                    <el-table-column prop="en_product_name" label="英文品名">
                    </el-table-column>
                    <el-table-column label="操作" fixed="right" width="250">
                        <template slot-scope="scope">
                            <el-button
                                type="primary"
                                size="mini"
                                @click="editProduct(scope)"
                                >编辑</el-button
                            >
                            <!-- <el-button
                                type="warning"
                                size="mini"
                                @click="editRemark(scope.row)"
                                >备注</el-button
                            > -->
                            <el-button
                                type="success"
                                size="mini"
                                @click="lookpic(scope.row)"
                                >查看图片</el-button
                            >
                            <el-button
                                v-if="isShowCustomsDeclaration"
                                type="success"
                                size="mini"
                                @click="lookpic(scope.row)"
                                >修改发票名称</el-button
                            >
                            <el-button
                                type="info"
                                size="mini"
                                @click="copyProduct(scope)"
                                >复制</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-card>
        <el-dialog
            title="产品库信息"
            :visible.sync="diaVisible"
            width="70%"
            :close-on-click-modal="false"
        >
            <ProductDetails
                ref="openDiaRef"
                @closeDialog="closeAddDialog"
                v-if="diaVisible"
            />
        </el-dialog>
        <el-dialog
            @close="closeRemarkDialog"
            title="备注"
            :visible.sync="remarkVisible"
            width="40%"
            :close-on-click-modal="false"
        >
            <el-input
                type="textarea"
                v-model="productDetails.remarks"
                placeholder="请输入"
                size="normal"
            ></el-input>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="remarkVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmRemark"
                    >确定</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            title="查看图片"
            :visible.sync="lookpicVisible"
            width="40%"
            @close="closeLookPic"
        >
            <div>
                <el-skeleton :loading="viewImgLoading" animated>
                    <template slot="template">
                        <el-skeleton-item
                            variant="image"
                            style="
                                height: 300px;
                                display: flex;
                                justify-content: center;
                            "
                        />
                    </template>
                    <template>
                        <el-carousel
                            height="300px"
                            arrow="always"
                            v-if="viewImg.length > 0 && !viewImgLoading"
                        >
                            <el-carousel-item
                                v-for="item in viewImg"
                                :key="item"
                            >
                                <div
                                    style="
                                        display: flex;
                                        justify-content: center;
                                    "
                                >
                                    <el-image
                                        style="width: 300px; height: 300px"
                                        :src="item"
                                        fit="scale-down"
                                        :preview-src-list="viewImg"
                                    ></el-image>
                                </div>
                            </el-carousel-item>
                        </el-carousel>
                        <div v-else>
                            <el-empty description="暂无图片"></el-empty>
                        </div>
                    </template>
                </el-skeleton>
                <div style="overflow-y: auto">
                    <!-- <vos-oss
                        :dir="dir"
                        :file-list="product_attachment"
                        :limit="99"
                        :fileSize="10"
                        :multiple="true"
                        list-type="text"
                        :disabled="true"
                        filesType=""
                        :showFileList="true"
                        :is_download="true"
                        :showName="true"
                        ref="look_visible_ref"
                    >
                    </vos-oss> -->
                    <div>
                        <ul
                            v-for="item in viewFile"
                            :key="item"
                            style="list-style: none"
                        >
                            <li>
                                {{ item.created_time }}
                                <span style="color: #007eff; margin: 0 20px"
                                    ><a
                                        :href="item.annex_url"
                                        target="_blank"
                                        >{{ item.annex_name }}</a
                                    ></span
                                >
                                {{
                                    item.audit_status == 2
                                        ? "已审核"
                                        : item.audit_status == 3
                                        ? "已驳回"
                                        : "审核中"
                                }}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <span slot="footer" style="display: flex; justify-content: center">
                <!-- <el-button @click="lookpicVisible = false">取消</el-button> -->
                <el-button type="primary" @click="lookpicVisible = false"
                    >确定</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            title="批量上传"
            :visible.sync="batch_upload_visible"
            width="50%"
            @close="CloseBatchUpload"
        >
            <vos-oss
                :showFileList="true"
                :limit="1"
                list-type="text"
                :filesType="'sheet'"
                :dir="dir"
                :file-list="fileList"
                ref="batch_upload_ref"
            >
                <el-button type="success">选择文件</el-button>
            </vos-oss>
            <span
                slot="footer"
                style="display: flex; justify-content: space-between"
            >
                <div>
                    <!-- https://images.wineyun.com/vinehoo/wiki/productexecl/%E4%BA%A7%E5%93%81%E6%89%B9%E9%87%8F%E4%B8%8A%E4%BC%A0%E6%A8%A1%E6%9D%BF.xlsx -->
                    <el-button type="info" size="default" @click="downloadtemp"
                        >下载模版</el-button
                    >
                </div>
                <div>
                    <el-button @click="batch_upload_visible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="ComfirmBatchUpload"
                        >确定</el-button
                    >
                </div>
            </span>
        </el-dialog>
        <el-dialog
            title="批量上传关单"
            :visible.sync="uploadCustomsVisible"
            width="50%"
        >
            <UploadCustomsDeclaration
                :visible.sync="uploadCustomsVisible"
                v-if="uploadCustomsVisible"
            />
        </el-dialog>

        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page"
                :page-size="limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
import ProductDetails from "./ProductDetails.vue";
import VosOss from "vos-oss";
import UploadCustomsDeclaration from "./UploadCustomsDeclaration.vue";
export default {
    components: { ProductDetails, VosOss, UploadCustomsDeclaration },
    data() {
        return {
            remark: "",
            yesOrNo: {
                0: "否",
                1: "是",
            },
            viewFile: [],
            diaVisible: false,
            batch_upload_visible: false,
            tableData: [],
            typeList: [
                { label: "条码", value: "bar_code" },
                { label: "简码", value: "short_code" },
                { label: "中文品名", value: "cn_product_name" },
                { label: "英文品名", value: "en_product_name" },
            ],
            selectType: "bar_code",
            keyword: "",
            editVisible: false,
            editInfo: {},
            // totalPage:0,
            page: 1,
            total: 0,
            limit: 10,
            mid: "",
            id: 0,
            remarkVisible: false,
            productDetails: {},
            lookpicVisible: false,
            viewImg: [],
            viewImgLoading: false,
            viewImgClone: {
                // eslint-disable-next-line camelcase
                en_back_label_img: [],
                // eslint-disable-next-line camelcase
                frontal_label_img: [],
                // eslint-disable-next-line camelcase
                cn_back_label_img: [],
                // eslint-disable-next-line camelcase
                package_img: [],
            },
            issyncwms: "",
            multilple: [],
            merchantOption: [],
            dir: "vinehoo/wiki/product/",
            fileList: [],
            product_attachment: [],
            uploadCustomsVisible: false,
        };
    },
    mounted() {
        this.getProduct();
        this.getMerchantList();
    },
    filters: {
        productKeywordsId(val) {
            let arr = [];
            if (val.length > 0) {
                val.forEach((item) => {
                    arr.push(item.name);
                });
                return arr.join(",");
            } else {
                return "-";
            }
        },
    },
    computed: {
        isShowCustomsDeclaration() {
            console.log("3333333-----", this.$route);

            return this.$route.meta.operations.includes("xgfpmc");
        },
    },
    methods: {
        downloadtemp() {
            let url =
                process.env.NODE_ENV === "development"
                    ? "https://test-wine.wineyun.com"
                    : "https://callback.vinehoo.com";
            window.open(url + "/wine-wiki/wiki/v3/product/excel/template");
        },
        CloseBatchUpload() {
            this.$refs.batch_upload_ref.handleviewFileList([]);
            this.fileList = [];
        },
        ComfirmBatchUpload() {
            console.warn(this.fileList);
            if (this.fileList.length > 0) {
                let file = this.fileList.join(",");
                this.$request.product
                    .exceldatacreate({
                        file,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.batch_upload_visible = false;
                            this.$message.success("上传成功");
                        }
                    });
            } else {
                this.$message.warning("请先选择文件");
            }
        },
        BatchUpload() {},
        getMerchantList() {
            this.$request.product.getMerchantList().then((res) => {
                if (res.data.error_code === 0) {
                    this.merchantOption = res.data.data.list;
                }
            });
        },
        handleSelectionChange(val) {
            this.multilple = val;
            console.warn(this.multilple);
        },
        pushTP() {
            let tempArr = [];
            tempArr = this.multilple.map((item) => {
                return item.id;
            });
            this.$request.product.syncwms({ ids: tempArr }).then((result) => {
                let hasSync = true;
                let showArr = [];
                result.data.data.map((item) => {
                    if (item.status == 1) {
                        showArr.push({
                            cn_product_name: item.cn_product_name,
                            msg: item.msg,
                        });
                        hasSync = false;
                    }
                });
                if (hasSync) {
                    this.$message({
                        message: "同步成功",
                        type: "success",
                    });
                } else {
                    showArr.map((item) => {
                        this.$message.error(item.cn_product_name + item.msg);
                    });
                }
                this.getProduct();
            });
        },
        selectable(row) {
            /*根据已退金额来判断是否可以退款多选*/
            if (row.issyncwms == 0 || row.issyncwms == 2) {
                return true;
            } else {
                return false;
            }
            // 1已同步 0未同步 2未同步成功
        },
        closeLookPic() {
            this.viewImg = [];
            this.$refs.look_visible_ref.handleviewFileList([]);
            this.product_attachment = [];
        },
        closeRemarkDialog() {
            this.productDetails = {};
        },
        comfirmRemark() {
            delete this.productDetails.created_time;
            delete this.productDetails.update_time;
            this.$request.product
                .updateProduct({
                    ...this.productDetails,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.remarkVisible = false;
                        this.$message({
                            message: "操作成功",
                            type: "success",
                        });
                    }
                });
        },
        lookpic(row) {
            this.lookpicVisible = true;
            this.viewImgLoading = true;
            this.getProductDetail(row.id).then((res) => {
                let img = [
                    "en_back_label_img",
                    "frontal_label_img",
                    "cn_back_label_img",
                    "package_img",
                ];
                img.forEach((item) => {
                    res.data.data[item];
                    if (
                        res.data.data[item] != null &&
                        res.data.data[item] != ""
                    ) {
                        this.viewImg.push(res.data.data[item]);
                        this.viewFile = res.data.data.annex.map((element) => {
                            // 在回调函数中对数组进行替换操作
                            return element;
                        });
                    }
                });
                this.viewFile = res.data.data.annex.map((element) => {
                    // 在回调函数中对数组进行替换操作
                    return element;
                });
                this.product_attachment = res.data.data.product_attachment
                    ? res.data.data.product_attachment.split(",")
                    : [];
                this.viewImgLoading = false;
            });
        },
        getProductDetail(id) {
            return new Promise((resolve, reject) => {
                this.$request.product
                    .getProductDetails({ id })
                    .then((res1) => {
                        resolve(res1);
                    })
                    .catch((e) => {
                        reject(e);
                    });
            });
        },
        editRemark(row) {
            // eslint-disable-next-line no-debugger
            this.remarkVisible = true;
            this.$request.product.getGoodstypeSelect().then((res) => {
                if (res.data.error_code == 0) {
                    this.getProductDetail(row.id).then((res1) => {
                        this.productDetails = res1.data.data;
                        this.productDetails.product_type_code =
                            this.productDetails.productTypeCode;
                        res.data.data.list.map((item) => {
                            if (
                                this.productDetails.product_category == item.id
                            ) {
                                this.productDetails.product_category_code =
                                    item.code;
                            }
                        });
                    });
                }
            });
        },
        handleSizeChange(val) {
            this.limit = val;
            this.page = 1;
            console.log(`每页 ${val} 条`);
            this.getProduct();
        },
        handleCurrentChange(val) {
            this.page = val;
            this.getProduct();
        },

        closeEditDialog() {
            this.editVisible = false;
            setTimeout(() => {
                // this.queryProduct();
                this.getProduct(this.keyword);
            }, 1000);
        },
        closeAddDialog() {
            this.diaVisible = false;
            // this.queryProduct();
            this.getProduct(this.keyword);
        },
        viewInventory(scope) {
            console.info(scope);
        },
        editProduct(scope) {
            this.diaVisible = true;
            // this.editInfo = scope.row;
            this.$nextTick(() => {
                this.$refs["openDiaRef"].getEditData(scope.row.id, "edit");
            });
        },
        printBarcode(scope) {
            console.info(scope);
        },
        copyProduct(scope) {
            this.diaVisible = true;
            this.$nextTick(() => {
                this.$refs["openDiaRef"].getEditData(scope.row.id, "copy");
            });
        },
        p(scope) {
            console.info(scope);
        },
        queryProduct() {
            this.page = 1;
            // this.limit = 15;
            this.getProduct();
        },
        getProduct() {
            let params = {};
            if (this.keyword) {
                params[this.selectType] = this.keyword;
            }
            params["limit"] = this.limit;
            params["page"] = this.page;
            // params['issyncwms'] = this.issyncwms;
            this.$request.product
                .getProductList({ ...params, mid: this.mid })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.tableData = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },

        addProcution() {
            this.diaVisible = true;
            this.$nextTick(() => {
                this.$refs["openDiaRef"].getGoodstypeSelect();
            });
        },
        // closeDialog() {
        //   this.$nextTick(() => {
        //     this.$refs["openDiaRef"].resetForm("ruleForm");
        //   });
        // },
    },
};
</script>

<style lang="scss" scoped>
.el-card {
    margin-bottom: 10px;
}
.demo-table-expand {
    padding: 10px 30px;
    font-size: 0;
}
.demo-table-expand {
    /deep/.el-form-item__label {
        width: 100px;
        color: #99a9bf;
    }
}

.demo-table-expand {
    /deep/.el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 33%;
        // height: 5px;
    }
    /deep/.el-form-item__content {
        // font-size: 12px;
        line-height: 30px;
    }
}
/deep/ .el-upload-list__item-name {
    max-width: 500px !important;
}
.filter {
    display: flex;
}
.table {
    margin-top: 10px;
}
</style>
