<template>
    <div class="goods-add-layout">
        <el-steps :active="step" align-center finish-status="success">
            <el-step title="选择产品类别"></el-step>
            <el-step title="填写基本信息资料"></el-step>
            <el-step title="上传图片及附件"></el-step>
        </el-steps>
        <div class="main-form step1" v-if="step == 0">
            <el-radio-group v-model="typeRadio">
                <el-radio
                    v-for="(item, index) in types"
                    :key="index"
                    :label="item.id"
                    >{{ item.name }}</el-radio
                >
            </el-radio-group>
            <div style="margin-top: 40px">
                <el-button @click="getPropertyList()" type="primary"
                    >下一步</el-button
                >
            </div>
        </div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            :inline="true"
            ref="ruleForm"
            label-width="140px"
            class="demo-ruleForm"
        >
            <div class="main-form" v-if="step == 1">
                <div class="deliver_container">
                    <div class="deliver_content">
                        <span class="deliver_text">基本信息</span>
                        <el-divider></el-divider>
                    </div>
                </div>
                <el-form-item label="渠道" v-if="rules.channel" prop="channel">
                    <el-select
                        v-model="ruleForm.channel"
                        filterable
                        placeholder="请选择渠道"
                        @change="changeChannel"
                        :disabled="isChannelEdit && isEdit"
                    >
                        <el-option
                            v-for="item in channelList"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="商家"
                    v-if="rules.merchants"
                    prop="merchants"
                >
                    <el-select
                        :disabled="ruleForm.channel === 1"
                        v-model="ruleForm.merchants"
                        filterable
                        multiple
                        reserve-keyword
                        placeholder="请选择商家"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in merchantsList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="中文品名"
                    v-if="rules.cn_product_name"
                    prop="cn_product_name"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.cn_product_name"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="英文品名"
                    v-if="rules.en_product_name"
                    prop="en_product_name"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.en_product_name"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="开票名称"
                    v-if="rules.invoice_name"
                    prop="invoice_name"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.invoice_name"
                    ></el-input>
                </el-form-item>

                <el-form-item
                    label="简码"
                    v-if="rules.short_code"
                    prop="short_code"
                >
                    <!-- :onkeyup="
                            (ruleForm.short_code = ruleForm.short_code.replace(
                                /\s+/g,
                                ''
                            ))
                        " -->
                    <el-input
                        class="w-280"
                        v-model.trim="ruleForm.short_code"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="葡萄采摘年份"
                    v-if="rules.grape_picking_years"
                    prop="grape_picking_years"
                >
                    <el-date-picker
                        value-format="yyyy"
                        v-model="ruleForm.grape_picking_years"
                        type="year"
                        placeholder="不填默认为NV"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item
                    label="产品类型"
                    v-if="rules.product_type"
                    prop="product_type"
                >
                    <!-- <el-select
                        v-model="ruleForm.product_type"
                        filterable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in typeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select> -->

                    <!-- @change="changeBarcode" -->
                    <el-cascader
                        :options="typeList"
                        v-model="ruleForm.product_type"
                        filterable
                        :show-all-levels="false"
                        :props="{
                            label: 'name',
                            value: 'id',
                            checkStrictly: true,
                        }"
                    >
                    </el-cascader>
                </el-form-item>
                <el-form-item
                    label="产品单位"
                    v-if="rules.product_unit"
                    prop="product_unit"
                >
                    <el-select
                        v-model="ruleForm.product_unit"
                        filterable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in unitList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item
                    label="产品形态"
                    v-if="rules.product_form"
                    prop="product_form"
                >
                    <el-radio-group
                        v-model="ruleForm.product_form"
                        style="width: 200px"
                    >
                        <el-radio
                            v-for="(item, index) in product_form"
                            :key="index"
                            :label="item.value"
                            >{{ item.name }}</el-radio
                        >
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    label="规格"
                    v-if="rules.capacity"
                    prop="capacity"
                >
                    <el-input-number
                        v-model="ruleForm.capacity"
                        :min="0"
                        :precision="3"
                        label="请填写规格"
                    ></el-input-number>
                    {{ ruleForm.product_form | capacityType }}
                </el-form-item>
                <el-form-item
                    v-if="rules.bar_code"
                    label="条码"
                    prop="bar_code"
                >
                    <el-input
                        :disabled="isEdit ? !ruleForm.bar_code_flag : false"
                        class="w-280"
                        v-model.trim="ruleForm.bar_code"
                    ></el-input>
                    <el-button
                        :disabled="isEdit ? !ruleForm.bar_code_flag : false"
                        style="margin-left: 10px"
                        type="primary"
                        @click="getBarCode"
                        >生成条码</el-button
                    >
                </el-form-item>
                <el-form-item
                    label="成本"
                    v-if="rules.costprice"
                    prop="costprice"
                >
                    <el-input-number
                        :min="0"
                        :precision="2"
                        v-model="ruleForm.costprice"
                    ></el-input-number>
                </el-form-item>
                <el-form-item
                    label="整箱成本"
                    v-if="rules.cost_container"
                    prop="cost_container"
                >
                    <el-input-number
                        :min="0"
                        :precision="2"
                        v-model="ruleForm.cost_container"
                    ></el-input-number>
                </el-form-item>

                <el-form-item
                    label="灌装日期"
                    v-if="rules.canning_years"
                    prop="canning_years"
                >
                    <el-date-picker
                        value-format="yyyy-MM-dd"
                        v-model="ruleForm.canning_years"
                        type="date"
                        placeholder="选择灌装日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item
                    v-if="rules.shelf_life"
                    label="保质期"
                    prop="shelf_life"
                >
                    <el-input-number
                        v-model="ruleForm.shelf_life"
                        :min="0"
                        :precision="0"
                        :max="100000"
                        label="保质期（天）"
                    ></el-input-number>
                    天
                </el-form-item>
                <el-form-item
                    v-if="rules.carton_dimension"
                    label="箱规"
                    prop="carton_dimension"
                >
                    <el-input-number
                        v-model="ruleForm.carton_dimension"
                        :min="0"
                        :max="100"
                        label="箱规"
                        :precision="0"
                    ></el-input-number>
                    瓶
                </el-form-item>
                <el-form-item label="重量" v-if="rules.weight" prop="weight">
                    <el-input-number
                        v-model="ruleForm.weight"
                        :min="0"
                        :precision="3"
                        label="重量（kg）"
                    ></el-input-number>
                    千克
                </el-form-item>

                <!-- <el-form-item
                    v-if="rules.is_gift"
                    label="是否赠品"
                    
                    prop="is_gift"
                >
                    <el-switch
                        v-model="ruleForm.is_gift"
                        active-color="#13ce66"
                        inactive-color="#999"
                    >
                    </el-switch>
                </el-form-item> -->
                <!-- <el-form-item
                    v-if="rules.is_addition"
                    label="是否为附加物"
                    
                    prop="is_addition"
                >
                    <el-switch
                        v-model="ruleForm.is_addition"
                        active-color="#13ce66"
                        inactive-color="#999"
                        :active-value="1"
                        :inactive-value="0"
                    >
                    </el-switch>
                </el-form-item> -->
                <el-form-item
                    v-if="rules.is_addition"
                    label="指令商品"
                    prop="is_addition"
                >
                    <el-switch
                        v-model="ruleForm.is_addition"
                        active-color="#13ce66"
                        inactive-color="#999"
                        :active-value="1"
                        :inactive-value="0"
                    >
                    </el-switch>
                </el-form-item>
                <el-form-item
                    label="长"
                    v-if="rules.product_length"
                    prop="length"
                >
                    <el-input-number
                        v-model="ruleForm.product_length"
                        :min="0"
                        :max="99999999"
                        :precision="2"
                        label="长"
                    ></el-input-number>
                    厘米
                </el-form-item>
                <el-form-item
                    label="宽"
                    v-if="rules.product_width"
                    prop="width"
                >
                    <el-input-number
                        v-model="ruleForm.product_width"
                        :min="0"
                        :max="99999999"
                        :precision="2"
                        label="宽"
                    ></el-input-number>
                    厘米
                </el-form-item>
                <el-form-item
                    label="高"
                    v-if="rules.product_height"
                    prop="high"
                >
                    <el-input-number
                        v-model="ruleForm.product_height"
                        :min="0"
                        :max="99999999"
                        :precision="2"
                        label="高"
                    ></el-input-number>
                    厘米
                </el-form-item>
                <el-form-item
                    label="是否同步T+"
                    v-if="rules.issync"
                    prop="issync"
                >
                    <el-select
                        v-model="ruleForm.issync"
                        filterable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in issyncList"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item
                    label="仓库"
                    v-if="rules.store_code"
                    prop="store_code"
                >
                    <el-select
                        v-model="ruleForm.store_code"
                        filterable
                        placeholder="请选择仓库"
                        multiple
                    >
                        <el-option
                            v-for="item in storeCodeList"
                            :key="item.store_code"
                            :label="item.store_name"
                            :value="item.store_code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item
                    label="京东ECLP商品编号"
                    v-if="rules.jd_eclp_code"
                >
                    <el-input
                        v-model="ruleForm.jd_eclp_code"
                        placeholder="请输入京东ECLP商品编号"
                        clearable
                    ></el-input>
                </el-form-item>
                <!-- tax_code -->
                <el-form-item
                    label="税目"
                    v-if="rules.tax_code"
                    prop="tax_code"
                >
                    <el-select
                        v-model="ruleForm.tax_code"
                        filterable
                        clearable
                        placeholder="请选择税目"
                        @change="taxCodeChange"
                    >
                        <el-option
                            v-for="item in taxItems"
                            :key="item.tax_code"
                            :label="item.tax_name"
                            :value="item.tax_code"
                        >
                        </el-option>
                    </el-select>
                    <el-checkbox
                        style="margin-left: 20px"
                        v-model="ruleForm.is_taxfree"
                        :true-label="1"
                        :false-label="0"
                        >免税</el-checkbox
                    >
                </el-form-item>
                <el-form-item
                    label="发票金额"
                    v-if="rules.invoice_value"
                    prop="invoice_value"
                >
                    <el-input-number
                        :min="0"
                        :precision="2"
                        v-model="ruleForm.invoice_value"
                    ></el-input-number>
                </el-form-item>
                <el-form-item
                    label="税率"
                    v-if="rules.tax_rate"
                    prop="tax_rate"
                >
                    <el-input-number
                        :min="0"
                        :step="0.01"
                        :precision="4"
                        v-model="ruleForm.tax_rate"
                        disabled
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="进口类型" prop="import_type">
                    <el-select
                        :value="ruleForm.import_type || ''"
                        @input="ruleForm.import_type = $event"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                {
                                    label: '地采',
                                    value: 1,
                                },
                                {
                                    label: '自进口',
                                    value: 2,
                                },
                                {
                                    label: '跨境',
                                    value: 3,
                                },
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="收款单位" prop="corp">
                    <el-select
                        :value="ruleForm.corp ? ruleForm.corp.split(',') : []"
                        @input="ruleForm.corp = $event.join()"
                        clearable
                        multiple
                    >
                        <el-option
                            v-for="item in [
                                {
                                    label: '佰酿云酒（重庆）科技有限公司',
                                    value: '001',
                                },
                                {
                                    label: '重庆云酒佰酿电子商务有限公司',
                                    value: '002',
                                },
                                {
                                    label: '渝中区微醺酒业商行',
                                    value: '008',
                                },
                                {
                                    label: '松鸽酒业（重庆）有限公司',
                                    value: '313',
                                },
                                {
                                    label: '木兰朵',
                                    value: '515',
                                },
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    v-if="!isEdit"
                    label="        "
                    prop="is_supplier_gift"
                >
                    <el-checkbox
                        style="margin-left: 20px"
                        v-model="ruleForm.is_supplier_gift"
                        :true-label="1"
                        :false-label="0"
                        >供应商赠品</el-checkbox
                    >
                </el-form-item>

                <div class="expansion_info">
                    <div class="deliver_container">
                        <div class="deliver_content">
                            <span class="deliver_text">拓展信息</span>
                            <el-divider></el-divider>
                        </div>
                    </div>
                    <el-form-item
                        label="国家"
                        v-if="rules.country_id"
                        prop="country_id"
                    >
                        <el-select
                            v-model="ruleForm.country_id"
                            filterable
                            style="width: 280px"
                            remote
                            clearable
                            reserve-keyword
                            placeholder="请输入国家"
                            :remote-method="getCountryList"
                            :loading="loading"
                        >
                            <el-option
                                v-for="item in CountryOptions"
                                :key="item.id"
                                :label="
                                    item.country_name_cn + item.country_name_en
                                "
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        v-if="rules.producing_area_id"
                        label="产区"
                        prop="producing_area_id"
                    >
                        <el-select
                            v-model="ruleForm.producing_area_id"
                            filterable
                            remote
                            style="width: 280px"
                            clearable
                            reserve-keyword
                            placeholder="请输入产区"
                            :remote-method="getRegionList"
                            :loading="loading"
                        >
                            <el-option
                                v-for="item in RegionOptions"
                                :key="item.id"
                                :label="
                                    item.regions_name_cn + item.regions_name_en
                                "
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="酒庄"
                        v-if="rules.chateau_id"
                        prop="chateau_id"
                    >
                        <el-select
                            v-model="ruleForm.chateau_id"
                            filterable
                            style="width: 280px"
                            clearable
                            remote
                            reserve-keyword
                            placeholder="请输入酒庄"
                            :remote-method="getWineryList"
                            :loading="loading"
                        >
                            <el-option
                                v-for="item in WineryOptions"
                                :key="item.id"
                                :label="
                                    item.winery_name_cn + item.winery_name_en
                                "
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <!-- <div v-if="this.typeRadio == 1"> -->
                    <el-form-item
                        label="葡萄品种"
                        prop="grape"
                        v-if="rules.grape_collection"
                    >
                        <el-select
                            style="width: 280px"
                            v-model="ruleForm.grape_collection"
                            filterable
                            remote
                            multiple
                            reserve-keyword
                            placeholder="请输入葡萄品种"
                            :remote-method="getGrapeList"
                            :loading="loading"
                        >
                            <el-option
                                v-for="item in GrapeOptions"
                                :key="item.id"
                                :label="item.gname_cn + item.gname_en"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="残糖"
                        v-if="rules.residual_sugar"
                        prop="residual_sugar"
                    >
                        <el-input-number
                            :precision="2"
                            v-model="ruleForm.residual_sugar"
                            label="残糖"
                        ></el-input-number>
                        g/L
                        <!-- <el-input
                            v-model="ruleForm.residual_sugar"
                            label="残糖"
                        ></el-input> -->
                    </el-form-item>
                    <el-form-item
                        label="酒精度"
                        v-if="rules.alcohol"
                        prop="alcohol"
                    >
                        <el-input-number
                            v-model="ruleForm.alcohol"
                            :min="0"
                            :max="100"
                            :precision="2"
                            label="酒精度"
                        ></el-input-number>
                        %vol
                        <!-- <el-input
                            v-model="ruleForm.alcohol"
                            label="酒精度"
                        ></el-input> -->
                    </el-form-item>
                    <el-form-item
                        label="关键词"
                        prop="grape"
                        v-if="rules.product_keywords_id"
                    >
                        <el-select
                            v-model="ruleForm.product_keywords_id"
                            filterable
                            remote
                            multiple
                            reserve-keyword
                            placeholder="请输入关键词"
                            :remote-method="getKeywrodList"
                            :loading="loading"
                        >
                            <el-option
                                v-for="item in keywordList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="酿造工艺"
                        v-if="rules.brewing"
                        prop="tasting_notes"
                    >
                        <el-input
                            class="w-280"
                            v-model="ruleForm.brewing"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="tasting_notes"
                        v-if="rules.tasting_notes"
                        prop="tasting_notes"
                    >
                        <el-input
                            type="textarea"
                            class="w-330"
                            :autosize="{ minRows: 2 }"
                            v-model="ruleForm.tasting_notes"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="评分" v-if="rules.score" prop="score">
                        <el-input
                            class="w-330"
                            :autosize="{ minRows: 2 }"
                            type="textarea"
                            v-model="ruleForm.score"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="获奖" v-if="rules.prize" prop="prize">
                        <el-input
                            class="w-330"
                            :autosize="{ minRows: 2 }"
                            type="textarea"
                            v-model="ruleForm.prize"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="饮用建议"
                        v-if="rules.drinking_suggestion"
                        prop="drinking_suggestion"
                    >
                        <el-input
                            class="w-330"
                            :autosize="{ minRows: 2 }"
                            type="textarea"
                            v-model="ruleForm.drinking_suggestion"
                        ></el-input>
                    </el-form-item>
                    <!-- </div> -->
                </div>
            </div>
            <div v-show="step == 2" class="main-form">
                <el-form-item
                    label="正标图"
                    v-if="rules.frontal_label_img"
                    prop="frontal_label_img"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :fileSize="10"
                        :limit="1"
                        :dir="dir"
                        :file-list="viewImg.frontal_label_img"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>

                <el-form-item
                    v-if="rules.cn_back_label_img"
                    label="中文背标图"
                    prop="cn_back_label_img"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :fileSize="10"
                        :dir="dir"
                        :limit="1"
                        :file-list="viewImg.cn_back_label_img"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>

                <el-form-item
                    v-if="rules.en_back_label_img"
                    label="英文背标图"
                    prop="en_back_label_img"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :fileSize="10"
                        :limit="1"
                        :dir="dir"
                        :file-list="viewImg.en_back_label_img"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>

                <el-form-item
                    v-if="rules.package_img"
                    label="包装图"
                    prop="package_img"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :fileSize="10"
                        :limit="1"
                        :dir="dir"
                        :file-list="viewImg.package_img"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                        <!-- <img
                            v-if="viewImg.frontal_label_img"
                            :src="viewImg.frontal_label_img"
                            class="avatar"
                        />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i> -->
                    </vos-oss>
                </el-form-item>

                <el-form-item
                    style="display: contents"
                    label="附件"
                    v-if="rules.product_attachment"
                    prop="product_attachment"
                >
                    <div class="annex-list">
                        <div>
                            <div
                                class="item"
                                v-for="(item, index) in displayAnnexTime"
                                :key="index"
                            >
                                {{ item }}
                            </div>
                        </div>
                        <div class="vos">
                            <vos-oss
                                :dir="dir"
                                :file-list="annexFileList"
                                :limit="99"
                                :fileSize="10"
                                :multiple="true"
                                list-type="text"
                                filesType=""
                                :showFileList="true"
                                :is_download="true"
                                :showName="true"
                            >
                                <el-button size="small" type="primary"
                                    >点击上传</el-button
                                >
                            </vos-oss>
                        </div>
                        <div>
                            <div
                                class="item"
                                v-for="(item, index) in displayAnnexStatus"
                                :key="index"
                            >
                                {{ statusFormatAnnex(item) }}
                            </div>
                        </div>
                    </div>
                </el-form-item>
            </div>
            <div style="text-align: center; margin-top: 60px">
                <div style="margin-top: 40px" v-if="step == 1">
                    <el-button @click="nextStep('ruleForm')" type="primary"
                        >下一步</el-button
                    >
                </div>
                <div v-if="step == 2">
                    <el-button type="primary" @click="submitForm()"
                        >立即{{ this.isEdit ? "更新" : "创建" }}</el-button
                    ><el-button @click="step = 1">上一步</el-button>
                </div>
            </div>
        </el-form>
        <el-dialog
            title="类似产品"
            :visible.sync="asSameProductVisible"
            width="50%"
            @close="closeSameProduct"
            :append-to-body="true"
        >
            <el-table
                :data="asSameProductList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="产品ID" prop="id"> </el-table-column>
                <el-table-column label="中文名" prop="cn_product_name">
                </el-table-column>
                <el-table-column label="英文名" prop="en_product_name">
                </el-table-column>
                <el-table-column label="简码" prop="short_code">
                </el-table-column>
                <el-table-column label="条码" prop="bar_code">
                </el-table-column>
            </el-table>
            <span slot="footer">
                <el-button @click="asSameProductVisible = false"
                    >取消创建</el-button
                >
                <el-button @click="forceUpdate" type="primary"
                    >确认创建</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import vosOss from "vos-oss";
export default {
    components: { vosOss },
    data() {
        return {
            loading: false,
            dir: "vinehoo/wiki/product/",
            testData: {},
            typeRadio: "",
            types: [],
            step: 0,
            // eslint-disable-next-line camelcase
            product_form: [],
            product_extra_image: [],
            uploadData: {
                // eslint-disable-next-line camelcase
                file_key: "file",
            },
            unitList: [], // 产品单位
            grapeList: [
                {
                    grape_name: "葡萄酒1",
                    grape_id: "1",
                },
                {
                    grape_name: "葡萄酒2",
                    grape_id: "2",
                },
                {
                    grape_name: "葡萄酒3",
                    grape_id: "3",
                },
            ],
            dieUnitList: [
                {
                    id: "g",
                    name: "克",
                },
                {
                    id: "ml",
                    name: "毫升",
                },
            ],
            dieUnitValue: "ml",
            typeList: [], // 产品类型
            issyncList: [], // 是否同步选项
            fileList: [],
            annexFileList: [],
            productKeyIdList: [],
            labelWidth: "140px",
            viewImg: {
                // eslint-disable-next-line camelcase
                en_back_label_img: [],
                // eslint-disable-next-line camelcase
                frontal_label_img: [],
                // eslint-disable-next-line camelcase
                cn_back_label_img: [],
                // eslint-disable-next-line camelcase
                package_img: [],
            },
            type1List: [],
            uncopyForm: [
                "bar_code",
                "short_code",
                // "country_id",
                // "producing_area_id",
                // "chateau_id",
                // "grape",
                // "residual_sugar",
                // "alcohol",
                // "brewing",
                // "tasting_notes",
                // "prize",
                // "score",
                // "drinking_suggestion",
                // "product_keywords_id",
            ],
            ruleForm: {
                cn_product_name: "",
                en_product_name: "",
                invoice_name: "",
                bar_code: "",
                short_code: "",
                grape_picking_years: "",
                canning_years: "",
                capacity: 750,
                costprice: 0,
                cost_container: 0,
                shelf_life: 0,
                weight: 0,
                country_id: "",
                producing_area_id: "",
                chateau_id: "",
                grape: "",
                product_type: "",
                product_category: "",
                product_category_code: "",
                // product_type_code: "",
                product_unit: "",
                product_length: 0,
                product_width: 0,
                product_height: 0,
                frontal_label_img: "",
                cn_back_label_img: "",
                en_back_label_img: "",
                package_img: "",
                product_extra_image: "",
                product_attachment: "",
                carton_dimension: 0,
                product_form: "",
                // is_gift: "",
                is_addition: "",
                product_keywords_id: [],
                residual_sugar: "",
                alcohol: "",
                brewing: "",
                tasting_notes: "",
                score: "",
                prize: "",
                drinking_suggestion: "",
                remarks: "",
                products_years: "",
                // eslint-disable-next-line no-dupe-keys
                produce_date: "",
                // store_code: [],
                grape_collection: [],
                issync: "",
                jd_eclp_code: "",
                merchants: [],
                channel: "",
                product_check: 0,
                tax_rate: 0,
                tax_code: "",
                import_type: "",
                corp: "",
                is_taxfree: 0,
                invoice_value: "",
                is_supplier_gift: 0,
            },

            rules: {},
            displayAnnexTime: [],
            displayAnnexStatus: [],
            isEdit: false,
            isChannelEdit: false,
            isCopy: false,
            grape: [],
            CountryOptions: [], //国家列表
            RegionOptions: [], //产区列表
            WineryOptions: [], //酒庄列表
            GrapeOptions: [],
            originTypeList: [],
            keywordList: [],
            storeCodeList: [],
            merchantsList: [],
            // 1平台 2自有 3酒云采购
            channelList: [],
            asSameProductVisible: false,
            asSameProductList: [],
            taxItems: [],
        };
    },
    filters: {
        capacityType(val) {
            if (val) {
                return "千克";
            } else {
                return "毫升";
            }
        },
    },
    mounted() {
        // this.getGoodstypeSelect();
        this.getMerchant();
        this.getTaxItems();
    },
    methods: {
        getTaxItems() {
            this.$request.product
                .getTaxItems({ page: 1, limit: 1000 })
                .then((res) => {
                    this.taxItems = res.data.data.list;
                });
        },
        taxCodeChange(val) {
            const item = this.taxItems.find((item) => item.tax_code === val);
            if (item) {
                this.ruleForm.tax_rate = item.tax_rate;
            }
        },
        changeChannel(params) {
            if (params === 1) {
                this.ruleForm.merchants = "";
            }
        },
        getMerchant(params) {
            this.$request.product
                .getMerchantList({
                    name: params || "",
                    shop_status: 1,
                })
                .then((res) => {
                    this.merchantsList = res.data.data.list;
                    console.warn(this.merchantsList);
                });
        },
        getKeywrodList(value) {
            this.$request.product
                .getKeyword({
                    keyword: value,
                })
                .then((res) => {
                    this.keywordList = res.data.data.list;
                    console.warn(res);
                });
        },
        getProductCategory() {
            return new Promise((resolve, reject) => {
                this.$request.product
                    .getProductCategory({
                        pid: this.typeRadio,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.originTypeList = res.data.data.list;
                            const list = res.data.data.list.map((item) => ({
                                ...item,
                            }));
                            const info = list.reduce((map, node) => {
                                map[node.id] = node;
                                if (list.some((item) => item.fid === node.id)) {
                                    node.children = [];
                                }
                                return map;
                            }, {});
                            this.typeList = list.filter((node) => {
                                info[node.fid] &&
                                    info[node.fid].children.push(node);
                                return !node.fid;
                            });
                            console.log("typeList", this.typeList);
                            // let typeArr = [];
                            // res.data.data.list.map((item) => {
                            //     if (item.fid === 0) {
                            //         typeArr.push(item);
                            //         item.children = [];
                            //     }
                            // });
                            // res.data.data.list.map((item3) => {
                            //     typeArr.map((item2) => {
                            //         if (item3.fid === item2.id) {
                            //             item2.children.push(item3);
                            //         }
                            //     });
                            // });
                            // this.typeList = typeArr;
                            resolve("ok");
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        statusFormatAnnex(status) {
            // 附件审批状态：0-未审批，1-审批中，2-审批通过，3-审批驳回
            const statusMap = {
                0: "未审批",
                1: "审批中",
                2: "审批通过",
                3: "审批驳回",
            };
            return statusMap[status];
        },
        getStoreList() {
            this.$request.product.getStoreList().then((res) => {
                if (res.data.error_code == 0) {
                    this.storeCodeList = res.data.data.list;
                    this.ruleForm.store_code = [
                        res.data.data.list[0].store_code,
                    ];
                }
            });
        },
        getGrapeList(keyword) {
            if (keyword) {
                this.$request.product
                    .getGrapeList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.GrapeOptions = res.data.data.list;
                        }
                    });
            }
        },
        //酒庄
        getWineryList(keyword) {
            if (keyword) {
                if (this.ruleForm.chateau_id) {
                    this.ruleForm.chateau_id = parseInt(
                        this.ruleForm.chateau_id
                    );
                }
                this.$request.product
                    .getWineryList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.WineryOptions = res.data.data.list;
                        }
                    });
            }
        },
        //产区
        getRegionList(keyword) {
            if (keyword) {
                if (this.ruleForm.producing_area_id) {
                    this.ruleForm.producing_area_id = parseInt(
                        this.ruleForm.producing_area_id
                    );
                }

                this.$request.product
                    .getRegionList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.RegionOptions = res.data.data.list;
                        }
                    });
            }
        },
        //国家
        getCountryList(keyword) {
            if (keyword) {
                if (this.ruleForm.country_id) {
                    this.ruleForm.country_id = parseInt(
                        this.ruleForm.country_id
                    );
                }
                this.$request.product
                    .getCountryList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.CountryOptions = res.data.data.list;
                        }
                    });
            }
        },
        getBarCode() {
            let product_type = "";
            if (this.ruleForm.product_type.length > 0) {
                product_type =
                    this.ruleForm.product_type[
                        this.ruleForm.product_type.length - 1
                    ];
            } else {
                this.$message({
                    message: "请选择产品类型",
                    type: "error",
                });
                return;
            }
            let product_type_code = "";
            this.originTypeList.map((item) => {
                if (item.id == product_type) {
                    product_type_code = item.code;
                }
            });
            let capacity = "";
            if (this.ruleForm.product_form) {
                capacity =
                    Math.floor(this.ruleForm.capacity * 100) / 100 + "kg";
            } else {
                capacity =
                    Math.floor(this.ruleForm.capacity * 100) / 100 + "ml";
            }
            this.$request.product
                .getBarCode({
                    capacity,
                    product_type_code,
                    grape_picking_years:
                        this.ruleForm.grape_picking_years || "NV",
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.ruleForm.bar_code = res.data.data.bar_code;
                    }
                });
        },
        resetForm(formName) {
            this.step = 0;
            this.$refs[formName].resetFields();
        },
        getPropertyList() {
            // 获取产品类型字段列表
            let data = {
                type_id: this.typeRadio,
            };
            this.$request.product.getPropertyList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.rules = res.data.data.list;
                    if (!this.isEdit) {
                        this.rules = Object.assign({}, this.rules, {
                            import_type: [
                                {
                                    required: true,
                                    message: "请选择进口类型",
                                    trigger: "change",
                                },
                            ],
                            // corp: [
                            //     {
                            //         required: true,
                            //         message: "请选择收款公司",
                            //         trigger: "change",
                            //     },
                            // ],
                        });
                    }
                    this.step = 1;
                    if (this.rules.product_form) {
                        this.product_form = this.rules.product_form[0].select;
                        if (!this.ruleForm.product_form) {
                            this.ruleForm.product_form =
                                this.product_form[0].value;
                        }
                    }
                    if (this.rules.issync) {
                        this.issyncList = this.rules.issync[0].select;
                        if (!this.ruleForm.issync) {
                            this.ruleForm.issync = this.issyncList[1].value;
                        }
                    }
                    if (this.rules.product_unit) {
                        this.unitList = this.rules.product_unit[0].list;
                        if (!this.ruleForm.product_unit) {
                            this.ruleForm.product_unit = this.unitList[0].id;
                        }
                    }
                    if (this.rules.channel) {
                        this.channelList = this.rules.channel[0].select;
                        if (!this.ruleForm.channel) {
                            this.ruleForm.channel = Number(
                                this.channelList[0].value
                            );
                        }
                    }
                    if (!this.isEdit && this.rules?.tax_code?.length) {
                        this.rules.tax_code[0].required = true;
                    }

                    // 拓展信息中的国家必填
                    if (this.rules?.country_id?.length) {
                        this.rules.country_id[0].required = true;
                        this.rules.country_id[0].message = "请选择国家";
                        this.rules.country_id[0].trigger = "change";
                    }

                    // 如果为酒类时，酒庄也是必填
                    if (
                        this.typeRadio === 1 &&
                        this.rules?.chateau_id?.length
                    ) {
                        this.rules.chateau_id[0].required = true;
                        this.rules.chateau_id[0].message = "请选择酒庄";
                        this.rules.chateau_id[0].trigger = "change";
                    }

                    for (let i in this.rules) {
                        delete this.rules[i][0].character_type;
                        delete this.rules[i][0].length;
                        delete this.rules[i][0].mutual_type;
                        delete this.rules[i][0].list;
                        delete this.rules[i][0].select;
                    }
                    this.getStoreList();
                }
            });
            this.getProductCategory().then(() => {
                if (this.isEdit || this.isCopy) {
                    if (
                        this.ruleForm.product_type != "" &&
                        this.ruleForm.product_type != null
                    ) {
                        function treeFindPath(tree, func, path = []) {
                            if (!tree) return [];
                            for (const data of tree) {
                                path.push(data.id);
                                if (func(data)) return path;
                                if (data.children) {
                                    const findChildren = treeFindPath(
                                        data.children,
                                        func,
                                        path
                                    );
                                    if (findChildren.length)
                                        return findChildren;
                                }
                                path.pop();
                            }
                            return [];
                        }
                        this.ruleForm.product_type = treeFindPath(
                            this.typeList,
                            (node) => node.id === this.ruleForm.product_type
                        );

                        // this.typeList.map((item) => {
                        //     if (this.ruleForm.product_type == item.id) {
                        //         this.ruleForm.product_type = [item.id];
                        //     } else {
                        //         if (item.children.length > 0) {
                        //             item.children.map((item2) => {
                        //                 if (
                        //                     this.ruleForm.product_type ==
                        //                     item2.id
                        //                 ) {
                        //                     this.ruleForm.product_type = [
                        //                         item2.fid,
                        //                         item2.id,
                        //                     ];
                        //                 }
                        //             });
                        //         }
                        //     }
                        // });
                    }
                }
            });
        },
        //复制 编辑
        getEditData(id, method) {
            this.step = 1;
            this.$request.product
                .getProductDetails({ id })
                .then((res) => {
                    if (method == "edit") {
                        this.isEdit = true;
                        this.ruleForm.id = res.data.data.id;
                        for (const key in this.ruleForm) {
                            for (const key1 in res.data.data)
                                if (key1 == key) {
                                    this.ruleForm[key] = res.data.data[key];
                                }
                            if (!res.data.data.bar_code) {
                                this.ruleForm.bar_code_flag = true;
                            }
                        }
                    } else {
                        this.isCopy = true;
                        for (const key in this.ruleForm) {
                            for (const key1 in res.data.data)
                                if (
                                    key1 == key &&
                                    !this.uncopyForm.includes(key)
                                ) {
                                    this.ruleForm[key] = res.data.data[key];
                                }
                        }
                    }
                    if (
                        res.data.data.grape_picking_years.toUpperCase() == "NV"
                    ) {
                        this.ruleForm.grape_picking_years = "";
                    }
                    //回显列表
                    if (
                        res.data.data.producing_area_id != 0 &&
                        res.data.data.producing_area_id != null
                    ) {
                        this.ruleForm.producing_area_id = Number(
                            res.data.data.producing_area_id
                        );
                        this.RegionOptions.push(
                            res.data.data.regions_resouce || {}
                        );
                    } else {
                        this.ruleForm.producing_area_id = "";
                    }
                    if (
                        res.data.data.country_id != 0 &&
                        res.data.data.country_id != null
                    ) {
                        this.ruleForm.country_id = Number(
                            res.data.data.country_id
                        );
                        this.CountryOptions.push(
                            res.data.data.country_resouce || {}
                        );
                    } else {
                        this.ruleForm.country_id = "";
                    }
                    if (
                        res.data.data.chateau_id != 0 &&
                        res.data.data.chateau_id != null
                    ) {
                        this.ruleForm.chateau_id = Number(
                            res.data.data.chateau_id
                        );
                        this.WineryOptions.push(
                            res.data.data.chateau_resouce || {}
                        );
                    } else {
                        this.ruleForm.chateau_id = "";
                    }
                    if (res.data.data.grapelist.length > 0) {
                        res.data.data.grapelist.map((item) => {
                            this.ruleForm.grape_collection.push(item.id);
                            this.GrapeOptions.push({
                                gname_cn: item.gname_cn,
                                gname_en: item.gname_en,
                                id: item.id,
                            });
                        });
                    }
                    if (res.data.data.product_keywords_id.length > 0) {
                        let product_keywords_id_arr = [];
                        res.data.data.product_keywords_id.map((item) => {
                            product_keywords_id_arr.push(item.id);
                            this.keywordList.push({
                                id: item.id,
                                name: item.name,
                            });
                        });
                        this.ruleForm.product_keywords_id = JSON.parse(
                            JSON.stringify(product_keywords_id_arr)
                        );
                    }
                    if (
                        this.ruleForm.issync !== "" &&
                        this.ruleForm.issync !== null
                    ) {
                        this.ruleForm.issync = this.ruleForm.issync.toString();
                    }

                    if (this.ruleForm.capacity) {
                        this.ruleForm.capacity = parseFloat(
                            this.ruleForm.capacity
                        );
                    }
                    if (res.data.data.merchant_resouce) {
                        let merchants = res.data.data.merchant_resouce.map(
                            (item) => {
                                return {
                                    id: item.mid,
                                    name: item.merchant_name,
                                };
                            }
                        );
                        let obj = {};
                        this.merchantsList = this.merchantsList
                            .concat(merchants)
                            .reduce((pre, cur) => {
                                if (!obj[cur.id]) {
                                    obj[cur.id] = true;
                                    return [...pre, cur];
                                } else {
                                    return pre;
                                }
                            }, []);
                        this.ruleForm.merchants =
                            res.data.data.merchant_resouce.map((item) => {
                                return item.mid;
                            });
                        if (this.isEdit) {
                            this.isChannelEdit =
                                this.ruleForm.merchants.length > 0
                                    ? true
                                    : false;
                        }
                    }
                    this.typeRadio = res.data.data.product_category;
                    let img = [
                        "en_back_label_img",
                        "frontal_label_img",
                        "cn_back_label_img",
                        "package_img",
                    ];
                    if (
                        this.ruleForm.product_attachment != null &&
                        this.ruleForm.product_attachment != ""
                    ) {
                        this.annexFileList =
                            this.ruleForm.product_attachment.split(",");
                    }
                    this.displayAnnexTime = [];
                    this.displayAnnexStatus = [];
                    res.data.data.annex.map((item) => {
                        this.displayAnnexTime.push(item.created_time);
                        this.displayAnnexStatus.push(item.audit_status);
                    });
                    img.forEach((item) => {
                        if (
                            this.ruleForm[item] != null &&
                            this.ruleForm[item] != ""
                        ) {
                            this.viewImg[item] = this.ruleForm[item].split(",");
                        }
                    });
                })
                .then(() => {
                    this.getGoodstypeSelect();
                    this.getPropertyList();
                });
        },
        retrycreatePartnerentity(params) {
            // 需要一个弹窗点击重试
            this.$confirm(params.msg + ",是否重试", "错误提示", {
                confirmButtonText: "重试",
                cancelButtonText: "取消",
                type: "error",
            }).then(() => {
                this.$request.product
                    .retrycreate({
                        id: this.ruleForm.id,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.$message.success("操作成功");
                            this.$emit("closeDialog");
                        } else {
                            this.retrycreatePartnerentity(params);
                        }
                    });
            });
        },
        submitForm() {
            this.$refs["ruleForm"].validate((valid) => {
                if (valid) {
                    // 添加对annexFileList的验证
                    if (this.annexFileList.some((file) => file.includes("+"))) {
                        this.$message.error(
                            '附件文件名不能包含"+"号，请重新上传'
                        );
                        return;
                    }
                    // eslint-disable-next-line camelcase
                    if (this.ruleForm.is_gift) {
                        // eslint-disable-next-line camelcase
                        this.ruleForm.is_gift = 1;
                    } else {
                        // eslint-disable-next-line camelcase
                        this.ruleForm.is_gift = 0;
                    }
                    let capacity = "";
                    if (this.ruleForm.product_form) {
                        capacity = this.ruleForm.capacity + "kg";
                    } else {
                        capacity = this.ruleForm.capacity + "ml";
                    }
                    let product_category_code = "";
                    this.types.map((item) => {
                        if (item.id == this.typeRadio) {
                            product_category_code = item.code;
                        }
                    });
                    let product_type = "";
                    if (this.ruleForm.product_type.length > 0) {
                        product_type =
                            this.ruleForm.product_type[
                                this.ruleForm.product_type.length - 1
                            ];
                    }
                    // this.ruleForm.product_type = this.ruleForm.product_type != ""
                    //     ? Number(this.ruleForm.product_type)
                    //     : this.ruleForm.product_type;
                    let product_type_code = "";
                    this.originTypeList.map((item) => {
                        if (item.id == product_type) {
                            product_type_code = item.code;
                        }
                    });

                    let product_keywords_id_arr = "";
                    if (this.ruleForm.product_keywords_id.length > 0) {
                        // this.ruleForm.product_keywords_id.map((item) => {
                        //     product_keywords_id_arr.push({ name: item });;
                        // });
                        product_keywords_id_arr =
                            this.ruleForm.product_keywords_id.join(",");
                    }
                    // return;
                    // eslint-disable-next-line no-unreachable
                    let product_extra_image =
                        this.product_extra_image.toString();
                    let submitViewImg = {
                        en_back_label_img:
                            this.viewImg.en_back_label_img.toString(),
                        frontal_label_img:
                            this.viewImg.frontal_label_img.toString(),
                        cn_back_label_img:
                            this.viewImg.cn_back_label_img.toString(),
                        package_img: this.viewImg.package_img.toString(),
                    };
                    let grape = this.ruleForm.grape_collection.join(",");

                    delete this.ruleForm.created_time;
                    delete this.ruleForm.update_time;
                    let data = {
                        ...this.ruleForm,
                        product_attachment: this.annexFileList.toString(),
                        product_category_code,
                        product_type_code,
                        product_type,
                        product_extra_image,
                        product_category: this.typeRadio,
                        capacity,
                        product_keywords_id: product_keywords_id_arr,
                        grape,
                        grape_picking_years:
                            this.ruleForm.grape_picking_years || "NV",
                        ...submitViewImg,
                    };
                    // console.warn(data.merchants)
                    // return;
                    // eslint-disable-next-line no-unreachable
                    this.isEdit ? (data.operator_id = 1) : "";
                    let subType = this.isEdit
                        ? "updateProduct"
                        : "createProduct";
                    this.$request.product[subType](data).then((res) => {
                        if (res.data.error_code === 0) {
                            if (res.data.data && res.data.data?.list) {
                                this.$message.warning(res.data.error_msg);
                                this.asSameProductVisible = true;
                                this.asSameProductList = res.data.data.list;
                            } else if (res.data.data.code == "1003") {
                                console.warn(res);
                                this.retrycreatePartnerentity(res.data.data);
                            } else {
                                this.resetForm("ruleForm");
                                this.$emit("closeDialog");
                                this.$message({
                                    message: "产品操作成功",
                                    type: "success",
                                });
                            }
                        } else {
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        forceUpdate() {
            this.asSameProductVisible = false;
            this.ruleForm.product_check = 1;
            this.submitForm();
        },
        getGoodstypeSelect() {
            // 获取产品主类型
            this.$request.product.getGoodstypeSelect().then((res) => {
                if (res.data.error_code == 0) {
                    this.types = res.data.data.list;
                    if (this.typeRadio === "") {
                        this.typeRadio = res.data.data.list[0].id;
                    }
                }
            });
        },
        nextStep(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.step = 2;
                }
            });
        },
        crateBarCodePrint() {
            let status = true;
            if (this.typeRadio === 1) {
                if (this.ruleForm.product_type && this.ruleForm.capacity) {
                    console.log("通过");
                } else {
                    status = false;
                }
            }
            if (!status) {
                this.$message.warning("请正确填写产品类型、规格后再生成条码");
                return;
            }
            let data = {
                grape_picking_years: this.ruleForm.grape_picking_years,
                capacity: this.ruleForm.capacity,
            };
            this.$request.goods.crateGoodsBarCode(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    // eslint-disable-next-line camelcase
                    this.ruleForm.bar_code = res.data.data.bar_code;
                }
            });
        },
        closeSameProduct() {
            this.asSameProductList = [];
            this.ruleForm.product_check = 0;
        },
    },
};
</script>
<style lang="scss" scoped>
.annex-list {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    .item {
        line-height: 28px;
    }
    .vos {
        margin: 0 40px;
    }
}
/deep/ .el-form-item {
    width: 48%;
}
>>> .el-message-box__wrapper
    .el-message-box
    .el-message-box__container
    .el-message-box__message
    p {
    word-break: break-all !important;
}
.avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    border: 1px dashed #d9d9d9;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    object-fit: cover;
    width: 100%;
    display: block;
}
/deep/ .w-280 {
    width: 280px !important;
}
/deep/ .w-330 {
    width: 330px !important;
}
.box-card {
    width: 200px;
}
.main-form {
    margin-top: 20px;
}
.step1 {
    text-align: center;
}
.grape_name {
    width: 80px;
}
.deliver_container {
    display: flex;
    align-items: center;
    justify-content: center;
}
.deliver_content {
    display: flex;
    align-items: center;
    width: 90%;
    justify-content: center;
    margin: 5px 0 20px 0;
    .deliver_text {
        width: 100px;
        font-size: 16px;
        font-weight: 600;
    }
}
</style>
